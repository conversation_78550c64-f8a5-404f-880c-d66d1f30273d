commit 5dcaaa2ee0f55334597fb421f8bf3d2384453dea
Author: T<PERSON> Casa Grande de Lima <<EMAIL>>
Date:   Mon Aug 25 08:41:46 2025 -0300

    `Added event listeners and functionality for clicking on calendar cells to open a modal for scheduling a new consultation with pre-filled date and time.`

diff --git a/src/components/ConsultaModal.vue b/src/components/ConsultaModal.vue
index f41fff7..a95539d 100644
--- a/src/components/ConsultaModal.vue
+++ b/src/components/ConsultaModal.vue
@@ -1018,6 +1018,26 @@ export default {
       // Garantir que o dropdown esteja fechado quando o modal abrir
       this.showPacienteDropdown = false;
     },
+    async abrirModalNovaConsultaComHorario(data, horario = null) {
+      this.resetForm();
+      this.isNewConsulta = true;
+
+      // Pré-preencher a data
+      if (data) {
+        const dataObj = new Date(data);
+        this.consulta.data = dataObj.toISOString().split('T')[0]; // Formato YYYY-MM-DD
+
+        // Se horário foi fornecido, pré-preencher também
+        if (horario) {
+          this.consulta.horario = horario; // Formato HH:MM
+        }
+      }
+
+      openModal('modalEditarConsulta');
+
+      // Garantir que o dropdown esteja fechado quando o modal abrir
+      this.showPacienteDropdown = false;
+    },
     async abrirModalEditarConsulta(consultaId) {
       try {
         this.isLoading = true;
diff --git a/src/views/Agenda.vue b/src/views/Agenda.vue
index e51a8a3..19488d3 100644
--- a/src/views/Agenda.vue
+++ b/src/views/Agenda.vue
@@ -187,6 +187,9 @@ export default {
     document.body.addEventListener('calendar.request.view', this.handleCalendarViewEvent);
     document.body.addEventListener('calendar.request.report', this.handleCalendarReportEvent);
     document.body.addEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);
+
+    // Adicionar event listener para cliques nas células do calendário
+    document.addEventListener('calendar:cell-clicked', this.handleCalendarCellClick);
   },
 
   beforeUnmount() {
@@ -194,6 +197,9 @@ export default {
     document.body.removeEventListener('calendar.request.view', this.handleCalendarViewEvent);
     document.body.removeEventListener('calendar.request.report', this.handleCalendarReportEvent);
     document.body.removeEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);
+
+    // Remover event listener para cliques nas células do calendário
+    document.removeEventListener('calendar:cell-clicked', this.handleCalendarCellClick);
   },
   methods: {
     ...mapMutations(["navbarMinimize"]),
@@ -840,6 +846,38 @@ export default {
         // Hide loading indicator
         this.isLoading.consultas = false;
       }
+    },
+
+    /**
+     * Método para abrir modal de nova consulta com data e horário pré-preenchidos
+     */
+    abrirModalConsultaComHorario(data, horario = null) {
+      console.log('Abrindo modal de consulta com:', { data, horario });
+      this.$refs.consultaModal.abrirModalNovaConsultaComHorario(data, horario);
+    },
+
+    /**
+     * Manipula cliques nas células do calendário
+     */
+    handleCalendarCellClick(event) {
+      console.log('Clique na célula do calendário:', event.detail);
+
+      const { date, time, view } = event.detail;
+
+      // Converter a data para o formato correto se necessário
+      let dataFormatada = date;
+      if (typeof date === 'string') {
+        dataFormatada = new Date(date);
+      }
+
+      // Abrir modal com data e horário pré-preenchidos
+      if (view === 'month') {
+        // Para visualização mensal, apenas a data (sem horário específico)
+        this.abrirModalConsultaComHorario(dataFormatada);
+      } else {
+        // Para visualizações diária e semanal, incluir o horário
+        this.abrirModalConsultaComHorario(dataFormatada, time);
+      }
     }
   }
 };
diff --git a/src/views/components/LumiCalendar/components/calendar/calendar-day-view.vue b/src/views/components/LumiCalendar/components/calendar/calendar-day-view.vue
index 00a4e61..cebc224 100644
--- a/src/views/components/LumiCalendar/components/calendar/calendar-day-view.vue
+++ b/src/views/components/LumiCalendar/components/calendar/calendar-day-view.vue
@@ -39,9 +39,11 @@
       </div>
       <!--day-row-cell-->
       <div
-        class="relative select-none day-cell w-full text-left border-b border-E0E0E0"
+        class="relative select-none day-cell w-full text-left border-b border-E0E0E0 calendar-cell-clickable"
         v-for="(day, dayindex) of [dateSelected]"
         :key="dayindex"
+        @click="handleCellClick(day, time)"
+        :title="`Agendar consulta para ${formatCellTitle(day, time)}`"
       >
         <!-- events are here -->
         <span class="block w-full min-h-2dt25 border-b border-F7F7F7" />
@@ -53,6 +55,8 @@
           :eventTime="time"
           :slots="slots"
         />
+        <!-- Overlay para hover -->
+        <div class="calendar-cell-overlay"></div>
       </div>
       <!--time-row-cell-->
       <div
@@ -87,6 +91,25 @@ import {
 const props = withDefaults(defineProps<Props>(), {
   dayTimes: () => [],
 });
+
+// Métodos para lidar com cliques nas células
+const handleCellClick = (date: Date, time: string) => {
+  // Emitir evento personalizado para o componente pai
+  const event = new CustomEvent('calendar:cell-clicked', {
+    detail: {
+      date: date,
+      time: time,
+      view: 'day'
+    },
+    bubbles: true
+  });
+  document.dispatchEvent(event);
+};
+
+const formatCellTitle = (date: Date, time: string) => {
+  const dateStr = date.toLocaleDateString('pt-BR');
+  return `${dateStr} às ${time}`;
+};
 </script>
 
 <style lang="scss" scoped>
@@ -96,4 +119,37 @@ const props = withDefaults(defineProps<Props>(), {
     transform: translateY(-0.5rem);
   }
 }
+
+/* Estilos para células clicáveis */
+.calendar-cell-clickable {
+  cursor: pointer;
+  transition: all 0.2s ease;
+  position: relative;
+
+  &:hover {
+    background-color: rgba(20, 112, 233, 0.05);
+
+    .calendar-cell-overlay {
+      opacity: 1;
+    }
+  }
+
+  .calendar-cell-overlay {
+    position: absolute;
+    top: 0;
+    left: 0;
+    right: 0;
+    bottom: 0;
+    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
+    opacity: 0;
+    transition: opacity 0.2s ease;
+    pointer-events: none;
+    border-radius: 2px;
+  }
+
+  &:active {
+    background-color: rgba(20, 112, 233, 0.1);
+    transform: scale(0.995);
+  }
+}
 </style>
diff --git a/src/views/components/LumiCalendar/components/calendar/calendar-month-view.vue b/src/views/components/LumiCalendar/components/calendar/calendar-month-view.vue
index 9d181de..53fdddb 100644
--- a/src/views/components/LumiCalendar/components/calendar/calendar-month-view.vue
+++ b/src/views/components/LumiCalendar/components/calendar/calendar-month-view.vue
@@ -31,7 +31,7 @@
     >
       <!--day-row-cell-->
       <div
-        class="relative select-none day-cell py-1 px-2 w-full min-h-5dt063 text-left border-b border-E0E0E0"
+        class="relative select-none day-cell py-1 px-2 w-full min-h-5dt063 text-left border-b border-E0E0E0 calendar-cell-clickable"
         v-for="(monthDayDate, monthdayindex) in Array.from(monthDays).slice(
           index * 7,
           line * 7
@@ -41,6 +41,8 @@
           'bg-FAFAFA': monthdayindex === 6,
         }"
         :key="monthdayindex"
+        @click="handleCellClick(monthDayDate)"
+        :title="`Agendar consulta para ${formatCellTitle(monthDayDate)}`"
       >
         <!-- events are here -->
         <span
@@ -61,6 +63,8 @@
           :eventDate="monthDayDate"
           :slots="slots"
         />
+        <!-- Overlay para hover -->
+        <div class="calendar-cell-overlay"></div>
       </div>
     </div>
   </div>
@@ -90,6 +94,25 @@ const props = withDefaults(defineProps<Props>(), {
   monthDays: () => [],
   weekDays: () => [],
 });
+
+// Métodos para lidar com cliques nas células
+const handleCellClick = (date: Date) => {
+  // Emitir evento personalizado para o componente pai
+  const event = new CustomEvent('calendar:cell-clicked', {
+    detail: {
+      date: date,
+      time: null, // Visualização mensal não tem horário específico
+      view: 'month'
+    },
+    bubbles: true
+  });
+  document.dispatchEvent(event);
+};
+
+const formatCellTitle = (date: Date) => {
+  const dateStr = date.toLocaleDateString('pt-BR');
+  return dateStr;
+};
 </script>
 
 <style lang="scss" scoped>
@@ -110,4 +133,37 @@ const props = withDefaults(defineProps<Props>(), {
   background: #0ea5e9;
   border-radius: 50%;
 }
+
+/* Estilos para células clicáveis */
+.calendar-cell-clickable {
+  cursor: pointer;
+  transition: all 0.2s ease;
+  position: relative;
+
+  &:hover {
+    background-color: rgba(20, 112, 233, 0.05) !important;
+
+    .calendar-cell-overlay {
+      opacity: 1;
+    }
+  }
+
+  .calendar-cell-overlay {
+    position: absolute;
+    top: 0;
+    left: 0;
+    right: 0;
+    bottom: 0;
+    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
+    opacity: 0;
+    transition: opacity 0.2s ease;
+    pointer-events: none;
+    border-radius: 2px;
+  }
+
+  &:active {
+    background-color: rgba(20, 112, 233, 0.1) !important;
+    transform: scale(0.995);
+  }
+}
 </style>
diff --git a/src/views/components/LumiCalendar/components/calendar/calendar-week-view.vue b/src/views/components/LumiCalendar/components/calendar/calendar-week-view.vue
index b8369f4..fb24731 100644
--- a/src/views/components/LumiCalendar/components/calendar/calendar-week-view.vue
+++ b/src/views/components/LumiCalendar/components/calendar/calendar-week-view.vue
@@ -56,7 +56,7 @@
       </div>
       <!--day-row-cell-->
       <div
-        class="relative select-none day-cell w-full text-left border-b border-E0E0E0"
+        class="relative select-none day-cell w-full text-left border-b border-E0E0E0 calendar-cell-clickable"
         v-for="(weekDayDate, weekindex) in weekDays"
         :class="{
           'border-r': weekindex !== weekDays.length - 1,
@@ -64,6 +64,8 @@
           selection: weekDayDate.getDate() === dateSelected.getDate(),
         }"
         :key="weekindex"
+        @click="handleCellClick(weekDayDate, time)"
+        :title="`Agendar consulta para ${formatCellTitle(weekDayDate, time)}`"
       >
         <!-- events are here -->
         <span class="block w-full min-h-2dt25 border-b border-F7F7F7" />
@@ -75,6 +77,8 @@
           :eventTime="time"
           :slots="slots"
         />
+        <!-- Overlay para hover -->
+        <div class="calendar-cell-overlay"></div>
       </div>
       <!--time-row-cell-->
       <div
@@ -111,6 +115,25 @@ const props = withDefaults(defineProps<Props>(), {
   dayTimes: () => [],
   weekDays: () => [],
 });
+
+// Métodos para lidar com cliques nas células
+const handleCellClick = (date: Date, time: string) => {
+  // Emitir evento personalizado para o componente pai
+  const event = new CustomEvent('calendar:cell-clicked', {
+    detail: {
+      date: date,
+      time: time,
+      view: 'week'
+    },
+    bubbles: true
+  });
+  document.dispatchEvent(event);
+};
+
+const formatCellTitle = (date: Date, time: string) => {
+  const dateStr = date.toLocaleDateString('pt-BR');
+  return `${dateStr} às ${time}`;
+};
 </script>
 
 <style lang="scss" scoped>
@@ -141,4 +164,37 @@ const props = withDefaults(defineProps<Props>(), {
     }
   }
 }
+
+/* Estilos para células clicáveis */
+.calendar-cell-clickable {
+  cursor: pointer;
+  transition: all 0.2s ease;
+  position: relative;
+
+  &:hover {
+    background-color: rgba(20, 112, 233, 0.05) !important;
+
+    .calendar-cell-overlay {
+      opacity: 1;
+    }
+  }
+
+  .calendar-cell-overlay {
+    position: absolute;
+    top: 0;
+    left: 0;
+    right: 0;
+    bottom: 0;
+    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
+    opacity: 0;
+    transition: opacity 0.2s ease;
+    pointer-events: none;
+    border-radius: 2px;
+  }
+
+  &:active {
+    background-color: rgba(20, 112, 233, 0.1) !important;
+    transform: scale(0.995);
+  }
+}
 </style>
